<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta name="x5-orientation" content="portrait">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    <meta name="browsermode" content="application">
    <meta name="wap-font-scale" content="no">
    <title>微信视频播放测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .video-container {
            position: relative;
            max-width: 600px;
            margin: 20px auto;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .video-wrapper {
            position: relative;
            width: 100%;
            padding-bottom: 56.25%; /* 16:9 */
        }
        
        .video-wrapper video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .test-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .browser-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .wechat-tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center my-4">微信视频播放测试</h1>
        
        <!-- 浏览器信息 -->
        <div class="browser-info">
            <h5><i class="fas fa-info-circle me-2"></i>浏览器信息</h5>
            <p id="browser-info">检测中...</p>
            <p id="user-agent" class="small text-muted"></p>
        </div>
        
        <!-- 微信提示 -->
        <div id="wechat-tips" class="wechat-tips d-none">
            <h5><i class="fab fa-weixin me-2 text-success"></i>微信用户提示</h5>
            <ul>
                <li>如果视频无法播放，请点击右上角菜单选择"在浏览器中打开"</li>
                <li>确保网络连接正常</li>
                <li>尝试点击视频播放按钮</li>
                <li>如果仍有问题，请刷新页面重试</li>
            </ul>
        </div>
        
        <!-- 测试视频 -->
        <div class="video-container">
            <div class="video-wrapper">
                <video
                    id="test-video"
                    controls
                    preload="none"
                    playsinline
                    webkit-playsinline
                    x5-video-player-type="h5"
                    x5-video-player-fullscreen="true"
                    x5-video-orientation="portraint"
                    x5-playsinline
                    poster="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/images/jyqk-sos-01.png">
                    <source src="https://jyqk.oss-cn-guangzhou.aliyuncs.com/video-player/videos/sample.mp4" type="video/mp4">
                    您的浏览器不支持HTML5视频播放。
                </video>
            </div>
        </div>
        
        <!-- 测试信息 -->
        <div class="test-info">
            <h5><i class="fas fa-clipboard-check me-2"></i>测试状态</h5>
            <div id="test-results">
                <p><span class="badge bg-secondary">等待测试</span> 视频加载状态</p>
                <p><span class="badge bg-secondary">等待测试</span> 播放功能</p>
                <p><span class="badge bg-secondary">等待测试</span> 控制器显示</p>
            </div>
        </div>
        
        <!-- 手动测试按钮 -->
        <div class="text-center my-4">
            <button id="play-btn" class="btn btn-primary me-2">
                <i class="fas fa-play me-1"></i>播放视频
            </button>
            <button id="pause-btn" class="btn btn-secondary me-2">
                <i class="fas fa-pause me-1"></i>暂停视频
            </button>
            <button id="reload-btn" class="btn btn-warning">
                <i class="fas fa-redo me-1"></i>重新加载
            </button>
        </div>
        
        <!-- 日志输出 -->
        <div class="test-info">
            <h5><i class="fas fa-terminal me-2"></i>调试日志</h5>
            <div id="debug-log" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;">
                <div>初始化测试...</div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 微信浏览器视频修复脚本 -->
    <script src="/js/wechat-video-fix.js"></script>
    
    <script>
        // 日志函数
        function log(message) {
            const logDiv = document.getElementById('debug-log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // 更新测试状态
        function updateTestStatus(test, status, type = 'success') {
            const results = document.getElementById('test-results');
            const badges = results.querySelectorAll('.badge');
            const statusClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-warning';
            
            badges.forEach((badge, index) => {
                if (index === test) {
                    badge.className = `badge ${statusClass}`;
                    badge.textContent = status;
                }
            });
        }
        
        // 浏览器检测
        function detectBrowser() {
            const ua = navigator.userAgent;
            const isWeChat = ua.toLowerCase().indexOf('micromessenger') !== -1;
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua);
            const isIOS = /iPad|iPhone|iPod/.test(ua);
            const isAndroid = /Android/.test(ua);
            
            let browserInfo = '';
            if (isWeChat) {
                browserInfo = '微信内置浏览器';
                document.getElementById('wechat-tips').classList.remove('d-none');
            } else if (isMobile) {
                browserInfo = '移动端浏览器';
            } else {
                browserInfo = '桌面端浏览器';
            }
            
            if (isIOS) browserInfo += ' (iOS)';
            if (isAndroid) browserInfo += ' (Android)';
            
            document.getElementById('browser-info').textContent = browserInfo;
            document.getElementById('user-agent').textContent = ua;
            
            log(`浏览器检测: ${browserInfo}`);
            log(`微信浏览器: ${isWeChat ? '是' : '否'}`);
            log(`移动设备: ${isMobile ? '是' : '否'}`);
        }
        
        // 初始化测试
        function initTest() {
            detectBrowser();
            
            const video = document.getElementById('test-video');
            const playBtn = document.getElementById('play-btn');
            const pauseBtn = document.getElementById('pause-btn');
            const reloadBtn = document.getElementById('reload-btn');
            
            // 视频事件监听
            video.addEventListener('loadstart', () => {
                log('视频开始加载');
                updateTestStatus(0, '加载中', 'warning');
            });
            
            video.addEventListener('loadedmetadata', () => {
                log('视频元数据加载完成');
                updateTestStatus(0, '元数据已加载', 'success');
            });
            
            video.addEventListener('loadeddata', () => {
                log('视频数据加载完成');
                updateTestStatus(0, '数据已加载', 'success');
            });
            
            video.addEventListener('canplay', () => {
                log('视频可以播放');
                updateTestStatus(0, '可以播放', 'success');
                updateTestStatus(2, '控制器正常', 'success');
            });
            
            video.addEventListener('play', () => {
                log('视频开始播放');
                updateTestStatus(1, '播放成功', 'success');
            });
            
            video.addEventListener('pause', () => {
                log('视频已暂停');
            });
            
            video.addEventListener('error', (e) => {
                log(`视频播放错误: ${e.message || '未知错误'}`);
                updateTestStatus(0, '加载失败', 'error');
                updateTestStatus(1, '播放失败', 'error');
            });
            
            video.addEventListener('waiting', () => {
                log('视频缓冲中...');
            });
            
            video.addEventListener('playing', () => {
                log('视频继续播放');
            });
            
            // 按钮事件
            playBtn.addEventListener('click', () => {
                video.play().catch(error => {
                    log(`播放失败: ${error.message}`);
                    updateTestStatus(1, '播放失败', 'error');
                });
            });
            
            pauseBtn.addEventListener('click', () => {
                video.pause();
            });
            
            reloadBtn.addEventListener('click', () => {
                log('重新加载视频');
                video.load();
                updateTestStatus(0, '重新加载', 'warning');
                updateTestStatus(1, '等待测试', 'secondary');
                updateTestStatus(2, '等待测试', 'secondary');
            });
            
            log('测试初始化完成');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initTest);
    </script>
</body>
</html>
