/**
 * 微信浏览器视频播放修复脚本
 * WeChat Browser Video Playback Fix
 * <AUTHOR>
 * @version 1.0.0
 */

(function() {
    'use strict';

    // 微信浏览器检测
    function isWeChatBrowser() {
        const ua = navigator.userAgent.toLowerCase();
        return ua.indexOf('micromessenger') !== -1;
    }

    // 移动设备检测
    function isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // iOS设备检测
    function isIOS() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent);
    }

    // Android设备检测
    function isAndroid() {
        return /Android/.test(navigator.userAgent);
    }

    // 微信版本检测
    function getWeChatVersion() {
        const ua = navigator.userAgent.toLowerCase();
        const match = ua.match(/micromessenger\/(\d+\.\d+\.\d+)/);
        return match ? match[1] : null;
    }

    // 修复微信浏览器视频播放问题
    function fixWeChatVideo() {
        if (!isWeChatBrowser()) {
            return;
        }

        console.log('检测到微信浏览器，启用视频播放修复');

        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initWeChatVideoFix);
        } else {
            initWeChatVideoFix();
        }
    }

    // 初始化微信视频修复
    function initWeChatVideoFix() {
        const videos = document.querySelectorAll('video');
        
        videos.forEach(function(video) {
            setupWeChatVideo(video);
        });

        // 监听动态添加的视频元素
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) {
                        const videos = node.querySelectorAll ? node.querySelectorAll('video') : [];
                        videos.forEach(function(video) {
                            setupWeChatVideo(video);
                        });
                        
                        if (node.tagName === 'VIDEO') {
                            setupWeChatVideo(node);
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 设置微信视频属性
    function setupWeChatVideo(video) {
        if (video.dataset.wechatFixed) {
            return; // 已经处理过
        }

        console.log('设置微信视频属性:', video);

        // 标记已处理
        video.dataset.wechatFixed = 'true';

        // 设置微信特有属性
        video.setAttribute('playsinline', '');
        video.setAttribute('webkit-playsinline', '');
        video.setAttribute('x5-video-player-type', 'h5');
        video.setAttribute('x5-video-player-fullscreen', 'true');
        video.setAttribute('x5-video-orientation', 'portraint');
        video.setAttribute('x5-playsinline', '');

        // 移除自动播放（微信不支持）
        video.removeAttribute('autoplay');
        video.autoplay = false;

        // 设置预加载策略
        video.preload = 'metadata';

        // 确保不静音（微信需要）
        video.muted = false;

        // 添加微信特殊事件处理
        addWeChatVideoEvents(video);

        // 修复视频源
        fixVideoSource(video);
    }

    // 添加微信视频事件
    function addWeChatVideoEvents(video) {
        // 点击播放处理
        video.addEventListener('click', function(e) {
            if (video.paused) {
                e.preventDefault();
                playVideoInWeChat(video);
            }
        });

        // 加载完成后显示播放提示
        video.addEventListener('loadedmetadata', function() {
            if (video.paused) {
                showWeChatPlayButton(video);
            }
        });

        // 播放开始时隐藏提示
        video.addEventListener('play', function() {
            hideWeChatPlayButton(video);
        });

        // 错误处理
        video.addEventListener('error', function(e) {
            console.error('微信视频播放错误:', e);
            showWeChatError(video, '视频加载失败，请检查网络连接');
        });

        // 网络状态变化处理
        video.addEventListener('stalled', function() {
            console.warn('微信视频播放停滞');
        });

        video.addEventListener('suspend', function() {
            console.warn('微信视频播放暂停');
        });
    }

    // 在微信中播放视频
    function playVideoInWeChat(video) {
        return video.play().catch(function(error) {
            console.error('微信播放失败:', error);
            
            // 尝试重新加载视频
            if (error.name === 'NotAllowedError') {
                showWeChatPlayButton(video, '请点击播放按钮开始播放');
            } else {
                video.load();
                setTimeout(function() {
                    video.play().catch(function(retryError) {
                        console.error('重试播放失败:', retryError);
                        showWeChatError(video, '播放失败，请刷新页面重试');
                    });
                }, 1000);
            }
        });
    }

    // 显示微信播放按钮
    function showWeChatPlayButton(video, message = '点击播放视频') {
        // 移除现有的播放按钮
        hideWeChatPlayButton(video);

        const playButton = document.createElement('div');
        playButton.className = 'wechat-play-overlay';
        playButton.innerHTML = `
            <div class="wechat-play-content">
                <div class="wechat-play-icon">
                    <svg width="60" height="60" viewBox="0 0 24 24" fill="white">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                </div>
                <div class="wechat-play-text">${message}</div>
            </div>
        `;

        // 设置样式
        playButton.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            z-index: 1000;
            border-radius: 8px;
        `;

        // 点击事件
        playButton.addEventListener('click', function(e) {
            e.stopPropagation();
            playVideoInWeChat(video);
        });

        // 添加到视频容器
        const container = video.parentNode;
        if (container.style.position !== 'relative' && container.style.position !== 'absolute') {
            container.style.position = 'relative';
        }
        container.appendChild(playButton);
    }

    // 隐藏微信播放按钮
    function hideWeChatPlayButton(video) {
        const container = video.parentNode;
        const overlay = container.querySelector('.wechat-play-overlay');
        if (overlay) {
            overlay.remove();
        }
    }

    // 显示微信错误信息
    function showWeChatError(video, message) {
        hideWeChatPlayButton(video);
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'wechat-video-error';
        errorDiv.innerHTML = `
            <div class="error-content">
                <i class="fas fa-exclamation-triangle"></i>
                <p>${message}</p>
                <button onclick="location.reload()" class="retry-btn">重试</button>
            </div>
        `;

        errorDiv.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            z-index: 1001;
            border-radius: 8px;
        `;

        const container = video.parentNode;
        container.appendChild(errorDiv);
    }

    // 修复视频源
    function fixVideoSource(video) {
        const sources = video.querySelectorAll('source');
        
        // 确保有正确的视频源
        if (sources.length === 0 && video.src) {
            const source = document.createElement('source');
            source.src = video.src;
            source.type = 'video/mp4';
            video.appendChild(source);
        }

        // 重新加载视频
        video.load();
    }

    // 添加CSS样式
    function addWeChatVideoStyles() {
        if (document.getElementById('wechat-video-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'wechat-video-styles';
        style.textContent = `
            .wechat-play-content {
                text-align: center;
                padding: 20px;
            }
            
            .wechat-play-icon {
                margin-bottom: 10px;
            }
            
            .wechat-play-text {
                font-size: 16px;
                font-weight: 500;
            }
            
            .wechat-video-error .error-content {
                text-align: center;
                padding: 20px;
            }
            
            .wechat-video-error .retry-btn {
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                margin-top: 10px;
                cursor: pointer;
            }
            
            @media (max-width: 768px) {
                .wechat-play-text {
                    font-size: 14px;
                }
            }
        `;
        
        document.head.appendChild(style);
    }

    // 初始化
    function init() {
        addWeChatVideoStyles();
        fixWeChatVideo();
        
        console.log('微信视频修复脚本已加载');
    }

    // 启动修复
    init();

})();
